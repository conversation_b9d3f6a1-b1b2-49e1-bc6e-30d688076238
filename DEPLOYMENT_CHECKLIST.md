# 🚀 PDFTools Pro - Deployment Checklist

## ✅ Pre-Deployment Checklist

### 1. **Backup Your Current Data**
```bash
# Run the backup script to create a complete backup
./backup-data.sh
```
**✅ This creates a timestamped backup of:**
- Database files (`storage.db`, `storage.db-shm`, `storage.db-wal`)
- Uploads directory with all user files
- Configuration files
- Compressed archive for easy transfer

### 2. **Server Preparation**
**✅ Ensure your server has:**
- [ ] Node.js 18+ installed
- [ ] npm installed
- [ ] PM2 installed globally (`npm install -g pm2`)
- [ ] Build tools installed (`build-essential` on Ubuntu)
- [ ] Proper user permissions for `/home/<USER>/htdocs/pdfzone.pro`

### 3. **File Transfer**
**✅ Upload to server:**
- [ ] All source code files
- [ ] `storage.db` (your database with all data)
- [ ] `storage.db-shm` and `storage.db-wal` (if they exist)
- [ ] `uploads/` directory (all user uploaded files)
- [ ] `package.json` and `package-lock.json`

## 🔧 Deployment Steps

### Step 1: Environment Configuration
```bash
# Copy environment template
cp .env.production .env

# Edit with your production settings
nano .env
```

**✅ Required Environment Variables:**
- [ ] `NODE_ENV=production`
- [ ] `PORT=5001`
- [ ] `SESSION_SECRET` (generate a secure random string)
- [ ] SMTP settings for email functionality
- [ ] Payment gateway credentials (Stripe, PayPal)
- [ ] Domain and security settings

### Step 2: Install Dependencies and Build
```bash
# Run the deployment script
./deploy.sh
```

**✅ This script will:**
- [ ] Check Node.js version (18+ required)
- [ ] Install all dependencies
- [ ] Build the application
- [ ] Set proper file permissions
- [ ] Create necessary directories

### Step 3: Start the Application
```bash
# Option 1: Direct start (for testing)
./start-production.sh

# Option 2: PM2 (recommended for production)
npm run pm2:start
```

### Step 4: Verify Deployment
```bash
# Check application health
curl http://localhost:5001/api/health

# Check PM2 status
pm2 status

# View logs
pm2 logs pdftools-pro
```

## 🌐 Web Server Configuration

### Nginx Configuration (Recommended)
Create `/etc/nginx/sites-available/pdfzone.pro`:
```nginx
server {
    listen 80;
    server_name pdfzone.pro www.pdfzone.pro;
    
    location / {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/pdfzone.pro /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 Security Setup

### SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d pdfzone.pro -d www.pdfzone.pro
```

### Firewall Configuration
```bash
# Configure UFW firewall
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

## 📊 Post-Deployment Verification

### 1. **Application Health Check**
- [ ] Visit: `http://pdfzone.pro:5001/api/health`
- [ ] Should return: `{"status":"ok","database":"connected",...}`

### 2. **Database Verification**
- [ ] Login with admin credentials: `admin` / `admin123`
- [ ] Verify all your data is present:
  - [ ] Users and accounts
  - [ ] PDF operations history
  - [ ] Checkout pages
  - [ ] Payment records
  - [ ] SMTP configurations

### 3. **Functionality Testing**
- [ ] User registration and login
- [ ] PDF processing features
- [ ] File uploads and downloads
- [ ] Email notifications
- [ ] Payment processing (if configured)

### 4. **Performance Monitoring**
```bash
# Monitor application
pm2 monit

# Check logs
pm2 logs pdftools-pro

# System resources
htop
df -h
```

## 🔄 PM2 Process Management

### Essential PM2 Commands
```bash
# Start application
pm2 start ecosystem.config.js --env production

# Stop application
pm2 stop pdftools-pro

# Restart application
pm2 restart pdftools-pro

# View logs
pm2 logs pdftools-pro

# Monitor resources
pm2 monit

# Auto-start on server reboot
pm2 startup
pm2 save
```

## 🆘 Troubleshooting

### Common Issues and Solutions

1. **Port 5001 already in use**
   ```bash
   sudo lsof -i :5001
   sudo kill -9 <PID>
   ```

2. **Permission denied errors**
   ```bash
   sudo chown -R pdfzone:pdfzone /home/<USER>/htdocs/pdfzone.pro
   chmod -R 755 /home/<USER>/htdocs/pdfzone.pro
   ```

3. **Database locked errors**
   ```bash
   ps aux | grep node
   sudo pkill -f "node.*pdftools"
   ```

4. **Module not found errors**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

## 📞 Final Verification

**✅ Your application should be accessible at:**
- Direct access: `http://pdfzone.pro:5001`
- Through reverse proxy: `http://pdfzone.pro` (after Nginx setup)
- With SSL: `https://pdfzone.pro` (after SSL setup)

**✅ Admin Access:**
- Username: `admin`
- Password: `admin123`
- **⚠️ IMPORTANT: Change the admin password immediately after deployment!**

**✅ Data Preservation Confirmed:**
- All user accounts preserved
- PDF processing history maintained
- Checkout pages and configurations intact
- Payment records preserved
- SMTP configurations maintained

## 🎉 Deployment Complete!

Your PDFTools Pro application is now successfully deployed with:
- ✅ SQLite database with all existing data
- ✅ Production-ready configuration
- ✅ PM2 process management
- ✅ Health monitoring
- ✅ Proper security settings
- ✅ All features functional

**Next Steps:**
1. Set up SSL certificate for HTTPS
2. Configure domain DNS to point to your server
3. Set up automated backups
4. Monitor application performance
5. Update admin credentials
