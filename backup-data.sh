#!/bin/bash

# PDFTools Pro Data Backup Script
# Creates a complete backup of your application data

echo "📦 Creating PDFTools Pro Data Backup..."
echo "======================================"

# Create backup directory with timestamp
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Backup database files
print_info "Backing up database files..."
if [ -f "storage.db" ]; then
    cp storage.db "$BACKUP_DIR/"
    print_status "Database backed up: storage.db"
else
    print_warning "No storage.db found"
fi

if [ -f "storage.db-shm" ]; then
    cp storage.db-shm "$BACKUP_DIR/"
    print_status "Database shared memory backed up"
fi

if [ -f "storage.db-wal" ]; then
    cp storage.db-wal "$BACKUP_DIR/"
    print_status "Database WAL file backed up"
fi

# Backup uploads directory
print_info "Backing up uploads directory..."
if [ -d "uploads" ]; then
    cp -r uploads "$BACKUP_DIR/"
    print_status "Uploads directory backed up"
else
    print_warning "No uploads directory found"
fi

# Backup configuration files
print_info "Backing up configuration files..."
files_to_backup=(
    ".env"
    ".env.production"
    "package.json"
    "package-lock.json"
    "ecosystem.config.js"
    "drizzle.config.ts"
    "tsconfig.json"
    "vite.config.ts"
    "tailwind.config.ts"
    "postcss.config.js"
)

for file in "${files_to_backup[@]}"; do
    if [ -f "$file" ]; then
        cp "$file" "$BACKUP_DIR/"
        print_status "Backed up: $file"
    fi
done

# Create a manifest file
print_info "Creating backup manifest..."
cat > "$BACKUP_DIR/BACKUP_INFO.txt" << EOF
PDFTools Pro Backup Information
===============================

Backup Date: $(date)
Backup Directory: $BACKUP_DIR
Server: $(hostname)
User: $(whoami)

Files Included:
EOF

# List all files in backup
find "$BACKUP_DIR" -type f -exec basename {} \; | sort >> "$BACKUP_DIR/BACKUP_INFO.txt"

# Calculate backup size
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)

echo ""
print_status "Backup completed successfully!"
echo ""
print_info "Backup location: ./$BACKUP_DIR"
print_info "Backup size: $BACKUP_SIZE"
echo ""
print_info "To restore this backup on your server:"
echo "1. Upload the entire '$BACKUP_DIR' folder to your server"
echo "2. Copy database files: cp $BACKUP_DIR/storage.db* ."
echo "3. Copy uploads: cp -r $BACKUP_DIR/uploads ."
echo "4. Copy configuration: cp $BACKUP_DIR/.env ."
echo ""

# Create a compressed archive
print_info "Creating compressed archive..."
tar -czf "${BACKUP_DIR}.tar.gz" "$BACKUP_DIR"
ARCHIVE_SIZE=$(du -sh "${BACKUP_DIR}.tar.gz" | cut -f1)

print_status "Compressed archive created: ${BACKUP_DIR}.tar.gz"
print_info "Archive size: $ARCHIVE_SIZE"
echo ""
print_info "You can upload either the folder or the .tar.gz file to your server"
