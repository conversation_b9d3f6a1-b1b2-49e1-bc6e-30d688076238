module.exports = {
  apps: [
    {
      name: 'pdftools-pro',
      script: 'dist/index.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 5001,
        USE_SQLITE: 'true',
        STORAGE_TYPE: 'sqlite'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5001,
        USE_SQLITE: 'true',
        STORAGE_TYPE: 'sqlite'
      },
      // Logging
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      
      // Advanced features
      source_map_support: true,
      instance_var: 'INSTANCE_ID',
      
      // Graceful shutdown
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Environment specific settings
      node_args: '--max-old-space-size=1024'
    }
  ],

  deploy: {
    production: {
      user: 'pdfzone',
      host: 'pdfzone.pro',
      ref: 'origin/main',
      repo: '**************:yourusername/pdftools-pro.git',
      path: '/home/<USER>/htdocs/pdfzone.pro',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
