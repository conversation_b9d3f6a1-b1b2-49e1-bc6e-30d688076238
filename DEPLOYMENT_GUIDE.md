# PDFTools Pro - Production Deployment Guide

## 🎯 Deployment Overview

**Domain**: pdfzone.pro  
**Server Path**: `/home/<USER>/htdocs/pdfzone.pro`  
**Port**: 5001  
**Database**: SQLite (Persistent)  
**Process Manager**: PM2  

## 📋 Prerequisites

### Server Requirements
- **Node.js**: Version 18+ 
- **npm**: Latest version
- **PM2**: Process manager for Node.js
- **Git**: For code deployment
- **Linux/Unix**: Ubuntu/CentOS/Debian

### System Dependencies
```bash
# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Install build essentials (for native modules)
sudo apt-get install -y build-essential python3
```

## 🚀 Deployment Steps

### Step 1: Upload Files to Server
Upload all your project files to `/home/<USER>/htdocs/pdfzone.pro/`

**Important Files to Include:**
- All source code files
- `storage.db` (your current database with all data)
- `storage.db-shm` and `storage.db-wal` (if they exist)
- `uploads/` directory (with all uploaded files)
- `package.json` and `package-lock.json`
- `.env.production` (rename to `.env` and configure)

### Step 2: Set Up Environment Configuration
```bash
# Navigate to your project directory
cd /home/<USER>/htdocs/pdfzone.pro

# Copy and configure environment file
cp .env.production .env

# Edit the .env file with your production settings
nano .env
```

**Required Environment Variables:**
```env
NODE_ENV=production
PORT=5001
USE_SQLITE=true
STORAGE_TYPE=sqlite
SESSION_SECRET=your-super-secure-session-secret
SMTP_HOST=your-smtp-host
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password
STRIPE_SECRET_KEY=your-stripe-secret-key
PAYPAL_CLIENT_ID=your-paypal-client-id
```

### Step 3: Install Dependencies and Build
```bash
# Make deployment script executable
chmod +x deploy.sh
chmod +x start-production.sh

# Run deployment script
./deploy.sh
```

### Step 4: Set Up File Permissions
```bash
# Set proper ownership
sudo chown -R pdfzone:pdfzone /home/<USER>/htdocs/pdfzone.pro

# Set directory permissions
find /home/<USER>/htdocs/pdfzone.pro -type d -exec chmod 755 {} \;

# Set file permissions
find /home/<USER>/htdocs/pdfzone.pro -type f -exec chmod 644 {} \;

# Make scripts executable
chmod +x deploy.sh start-production.sh

# Set database permissions
chmod 644 storage.db*

# Set upload directory permissions
chmod -R 755 uploads/
```

### Step 5: Start Application with PM2
```bash
# Start the application
npm run pm2:start

# Check status
npm run pm2:status

# View logs
npm run pm2:logs
```

## 🔧 PM2 Management Commands

### Basic Commands
```bash
# Start application
pm2 start ecosystem.config.js --env production

# Stop application
pm2 stop pdftools-pro

# Restart application
pm2 restart pdftools-pro

# View logs
pm2 logs pdftools-pro

# Monitor application
pm2 monit

# Check status
pm2 status
```

### Auto-Start on Server Reboot
```bash
# Generate startup script
pm2 startup

# Save current PM2 processes
pm2 save
```

## 🗄️ Database Information

Your SQLite database (`storage.db`) contains:
- **Users**: Admin and user accounts
- **PDF Operations**: Processing history
- **Checkout Pages**: Payment configurations
- **SMTP Configs**: Email settings
- **Payments**: Transaction records
- **System Settings**: Application configuration

**Database is automatically preserved during deployment.**

## 🌐 Web Server Configuration

### Nginx Configuration (Recommended)
```nginx
server {
    listen 80;
    server_name pdfzone.pro www.pdfzone.pro;
    
    location / {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Apache Configuration (Alternative)
```apache
<VirtualHost *:80>
    ServerName pdfzone.pro
    ServerAlias www.pdfzone.pro
    
    ProxyPreserveHost On
    ProxyRequests Off
    ProxyPass / http://localhost:5001/
    ProxyPassReverse / http://localhost:5001/
</VirtualHost>
```

## 🔒 Security Considerations

### Firewall Configuration
```bash
# Allow only necessary ports
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d pdfzone.pro -d www.pdfzone.pro
```

## 📊 Monitoring and Logs

### Log Locations
- **Application Logs**: `./logs/`
- **PM2 Logs**: `~/.pm2/logs/`
- **System Logs**: `/var/log/`

### Monitoring Commands
```bash
# Real-time logs
pm2 logs pdftools-pro --lines 100

# Application metrics
pm2 monit

# System resources
htop
df -h
```

## 🔄 Updates and Maintenance

### Updating the Application
```bash
# Stop the application
pm2 stop pdftools-pro

# Pull latest changes (if using Git)
git pull origin main

# Install dependencies
npm install

# Build application
npm run build

# Start application
pm2 start pdftools-pro
```

### Database Backup
```bash
# Create backup
cp storage.db storage.db.backup.$(date +%Y%m%d_%H%M%S)

# Automated backup script
echo "0 2 * * * cp /home/<USER>/htdocs/pdfzone.pro/storage.db /home/<USER>/backups/storage.db.backup.\$(date +\%Y\%m\%d_\%H\%M\%S)" | crontab -
```

## 🆘 Troubleshooting

### Common Issues

1. **Port 5001 already in use**
   ```bash
   sudo lsof -i :5001
   sudo kill -9 <PID>
   ```

2. **Permission denied errors**
   ```bash
   sudo chown -R pdfzone:pdfzone /home/<USER>/htdocs/pdfzone.pro
   ```

3. **Database locked errors**
   ```bash
   # Check for zombie processes
   ps aux | grep node
   # Kill if necessary
   sudo pkill -f "node.*pdftools"
   ```

4. **Module not found errors**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

### Health Check
```bash
# Check if application is running
curl http://localhost:5001/api/health

# Check PM2 status
pm2 status

# Check logs for errors
pm2 logs pdftools-pro --err
```

## 📞 Support

If you encounter any issues during deployment:
1. Check the logs: `pm2 logs pdftools-pro`
2. Verify environment variables in `.env`
3. Ensure all file permissions are correct
4. Check that port 5001 is available

Your application will be accessible at: **http://pdfzone.pro:5001**

After setting up a reverse proxy (Nginx/Apache), it will be available at: **http://pdfzone.pro**
