#!/bin/bash

# PDFTools Pro Production Startup Script
# Domain: pdfzone.pro
# Port: 5001

echo "🚀 Starting PDFTools Pro in Production Mode..."
echo "============================================="

# Load environment variables from .env file
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    echo "✅ Environment variables loaded from .env"
else
    echo "⚠️  No .env file found, using defaults"
fi

# Set production environment
export NODE_ENV=production
export USE_SQLITE=true
export STORAGE_TYPE=sqlite
export PORT=5001

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🗄️  Database: SQLite (storage.db)${NC}"
echo -e "${GREEN}🌐 Environment: ${NODE_ENV}${NC}"
echo -e "${GREEN}🔌 Port: ${PORT}${NC}"
echo -e "${GREEN}📁 Storage Type: ${STORAGE_TYPE}${NC}"
echo ""

# Check if built files exist
if [ ! -d "dist" ]; then
    echo "❌ Built files not found. Please run 'npm run build' first."
    exit 1
fi

# Check if database exists
if [ -f "storage.db" ]; then
    echo -e "${BLUE}✅ Database found: storage.db${NC}"
else
    echo -e "${BLUE}ℹ️  No existing database found. A new one will be created.${NC}"
fi

# Create uploads directory if it doesn't exist
mkdir -p uploads/temp

echo ""
echo "🎯 Starting server..."
echo "Access your application at: http://localhost:5001"
echo "Press Ctrl+C to stop the server"
echo ""

# Start the application
node dist/index.js
