#!/bin/bash

# PDFTools Pro Deployment Script
# Domain: pdfzone.pro
# Server Path: /home/<USER>/htdocs/pdfzone.pro

echo "🚀 Starting PDFTools Pro Deployment..."
echo "=================================="

# Set environment variables
export NODE_ENV=production
export USE_SQLITE=true
export STORAGE_TYPE=sqlite
export PORT=5001

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version check passed: $(node -v)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "npm is available: $(npm -v)"

# Install dependencies
print_info "Installing dependencies..."
npm install --production=false

if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Build the application
print_info "Building the application..."
npm run build

if [ $? -eq 0 ]; then
    print_status "Application built successfully"
else
    print_error "Failed to build application"
    exit 1
fi

# Check if storage.db exists
if [ -f "storage.db" ]; then
    print_status "Database file found: storage.db"
    print_info "Database size: $(du -h storage.db | cut -f1)"
else
    print_warning "No existing database found. A new one will be created."
fi

# Set proper file permissions
print_info "Setting file permissions..."
chmod +x deploy.sh
chmod +x start-production.sh
chmod 644 storage.db* 2>/dev/null || true
chmod 755 uploads/ 2>/dev/null || mkdir -p uploads && chmod 755 uploads/
chmod 755 uploads/temp/ 2>/dev/null || mkdir -p uploads/temp && chmod 755 uploads/temp/

print_status "File permissions set"

# Create uploads directory if it doesn't exist
mkdir -p uploads/temp
print_status "Upload directories created"

print_status "Deployment preparation completed!"
echo ""
echo "🎉 PDFTools Pro is ready for production!"
echo "=================================="
echo ""
print_info "Next steps:"
echo "1. Copy .env.production to .env and update with your settings"
echo "2. Run: ./start-production.sh"
echo "3. Or use PM2: pm2 start ecosystem.config.js"
echo ""
print_info "Application will run on: http://localhost:5001"
print_info "Database: SQLite (storage.db)"
print_info "Uploads: ./uploads/"
echo ""
